apiVersion: v1
kind: ConfigMap
metadata:
  name: test-django-api-config
  namespace: test-django-api-dev
  labels:
    app: test-django-api
    component: config
    environment: dev
    app-type: django-backend
data:
  # Application Configuration
  NODE_ENV: "dev"
  PORT: "8000"

  {{#eq APP_TYPE 'react-frontend'}}
  # React Frontend Configuration
  REACT_APP_API_URL: "{{API_URL}}"
  REACT_APP_ENVIRONMENT: "dev"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_TITLE: "Test Django API"
  PUBLIC_URL: "/"
  {{#eq APP_TYPE 'springboot-backend'}}
  # Spring Boot Backend Configuration
  SPRING_PROFILES_ACTIVE: "dev"
  SERVER_PORT: "8000"
  SPRING_APPLICATION_NAME: "test-django-api"

  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when-authorized"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"

  # JVM Configuration
  JAVA_OPTS: "-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseContainerSupport"

  # Application URLs
  APP_URL: "{{APP_URL}}"
  API_URL: "{{API_URL}}"
  {{#eq APP_TYPE 'django-backend'}}
  # Django Backend Configuration
  DJANGO_SETTINGS_MODULE: "test-django-api.settings.dev"
  ALLOWED_HOSTS: "{{ALLOWED_HOSTS}}"
  SECRET_KEY: "{{DJANGO_SECRET_KEY}}"

  # Django Database Configuration
  DATABASE_URL: "postgresql://postgres:password@localhost:5432/test_django_api"

  # Django Static Files
  STATIC_URL: "/static/"
  STATIC_ROOT: "/app/staticfiles/"
  MEDIA_URL: "/media/"
  MEDIA_ROOT: "/app/media/"

  # Django CORS Configuration
  CORS_ALLOWED_ORIGINS: "{{CORS_ORIGINS}}"
  CORS_ALLOW_CREDENTIALS: "true"

  # Django Application URLs
  APP_URL: "{{APP_URL}}"
  API_URL: "{{API_URL}}"
  {{#eq APP_TYPE 'nest-backend'}}
  # NestJS Backend Configuration
  NODE_ENV: "dev"
  APP_PORT: "8000"

  # NestJS Database Configuration
  DATABASE_URL: "postgresql://postgres:password@localhost:5432/test_django_api"
  DB_HOST: "localhost"
  DB_PORT: "5432"
  DB_NAME: "test_django_api"
  DB_USERNAME: "postgres"

  # NestJS Application URLs
  APP_URL: "{{APP_URL}}"
  API_URL: "{{API_URL}}"

  # NestJS CORS Configuration
  CORS_ORIGIN: "{{CORS_ORIGINS}}"
  CORS_CREDENTIALS: "true"
  {{#eq APP_TYPE 'nodejs-backend'}}
  # Other Backend Application Configuration
  APP_URL: "{{APP_URL}}"
  API_URL: "{{API_URL}}"

  {{#eq APP_TYPE 'springboot-backend'}}
  # Spring Boot Database Configuration
  SPRING_DATASOURCE_URL: "************************************************"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: "org.hibernate.dialect.PostgreSQLDialect"

  # Spring Boot Security & JWT
  JWT_EXPIRATION: "{{JWT_EXPIRATION}}"
  SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_REDIRECT_URI: "{{GOOGLE_REDIRECT_URI}}"
  SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_SCOPE: "{{OAUTH_SCOPES}}"

  # Spring Boot CORS Configuration
  CORS_ALLOWED_ORIGINS: "{{CORS_ORIGINS}}"
  CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
  CORS_ALLOW_CREDENTIALS: "true"
  CORS_MAX_AGE: "3600"

  # Spring Boot Mail Configuration
  SPRING_MAIL_HOST: "{{SMTP_HOST}}"
  SPRING_MAIL_PORT: "{{SMTP_PORT}}"
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_AUTH: "true"
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_STARTTLS_ENABLE: "true"
  {{#eq APP_TYPE 'django-backend'}}
  # Django Security & JWT Configuration
  JWT_EXPIRATION: "{{JWT_EXPIRATION}}"
  JWT_ALGORITHM: "HS256"

  # Django SMTP Configuration
  EMAIL_BACKEND: "django.core.mail.backends.smtp.EmailBackend"
  EMAIL_HOST: "{{SMTP_HOST}}"
  EMAIL_PORT: "{{SMTP_PORT}}"
  EMAIL_USE_TLS: "true"
  EMAIL_FROM: "{{SMTP_FROM}}"

  # Django OAuth2 Configuration
  GOOGLE_OAUTH2_CLIENT_ID: "1073981864538-********************************.apps.googleusercontent.com"
  GOOGLE_OAUTH2_REDIRECT_URI: "{{GOOGLE_REDIRECT_URI}}"
  SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE: "{{OAUTH_SCOPES}}"
  {{#eq APP_TYPE 'nest-backend'}}
  # NestJS Security & JWT Configuration
  JWT_EXPIRATION: "{{JWT_EXPIRATION}}"
  JWT_ALGORITHM: "HS256"

  # NestJS SMTP Configuration
  SMTP_HOST: "{{SMTP_HOST}}"
  SMTP_PORT: "{{SMTP_PORT}}"
  SMTP_FROM: "{{SMTP_FROM}}"
  SMTP_SECURE: "false"
  SMTP_TLS: "true"

  # NestJS OAuth2 Configuration
  GOOGLE_CLIENT_ID: "1073981864538-********************************.apps.googleusercontent.com"
  GOOGLE_REDIRECT_URI: "{{GOOGLE_REDIRECT_URI}}"
  OAUTH_SCOPES: "{{OAUTH_SCOPES}}"
  {{#eq APP_TYPE 'nodejs-backend'}}
  # Other Backend Applications - Generic Configuration
  DB_HOST: "localhost"
  DB_PORT: "5432"
  DB_NAME: "test_django_api"

  # JWT Configuration
  JWT_EXPIRATION: "{{JWT_EXPIRATION}}"

  # CORS Configuration
  CORS_ALLOWED_ORIGINS: "{{CORS_ORIGINS}}"
  CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
  CORS_ALLOW_CREDENTIALS: "true"
  CORS_MAX_AGE: "3600"

  # SMTP Configuration
  SMTP_HOST: "{{SMTP_HOST}}"
  SMTP_PORT: "{{SMTP_PORT}}"
  SMTP_FROM: "{{SMTP_FROM}}"

  # OAuth2 Configuration
  GOOGLE_REDIRECT_URI: "{{GOOGLE_REDIRECT_URI}}"
  GOOGLE_SCOPE: "{{OAUTH_SCOPES}}"
  OAUTH2_AUTHORIZED_REDIRECT_URIS: "{{OAUTH_REDIRECT_URIS}}"

