apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: test-django-api
  namespace: argocd
  labels:
    app.kubernetes.io/name: test-django-api
    app.kubernetes.io/part-of: test-django-api
    environment: dev
    app-type: django-backend
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: test-django-api-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: test-django-api/k8s
  destination:
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "django-backend"
  - name: Configuration

