apiVersion: v1
kind: Secret
metadata:
  name: test-django-api-secrets
  namespace: test-django-api-dev
  labels:
    app: test-django-api
    component: secrets
    environment: dev
    app-type: django-backend
type: Opaque
data:
  {{#eq APP_TYPE 'react-frontend'}}
  # React Frontend - Minimal secrets (typically API keys for build-time)
  # Most secrets are handled by the backend API
  # Add custom secrets here if needed for your React app

  # Custom Secret Keys (add manually if needed)
  # CUSTOM_SECRET_KEY: UExBQ0VIT0xERVI=  # PLACEHOLDER - Update with actual base64 encoded value

