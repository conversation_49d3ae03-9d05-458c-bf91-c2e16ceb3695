apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-django-api
  namespace: test-django-api-dev
  labels:
    app: test-django-api
    component: django-backend
    version: v1.0.0
    environment: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: test-django-api
      app.kubernetes.io/name: test-django-api
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: test-django-api
        app.kubernetes.io/name: test-django-api
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: django-backend
        version: v1.0.0
        environment: dev
    spec:
      {{#eq APP_TYPE 'react-frontend'}}
      # React Frontend - No init containers needed (stateless)
      containers:
      - name: test-django-api
        image: python:3.11-slim
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: test-django-api-config
        {{#eq APP_TYPE 'react-frontend'}}
        # React Frontend - Configure nginx to listen on the specified port
        env:
        - name: NGINX_PORT
          value: "8000"
        - name: LISTEN_PORT
          value: "8000"
        # Health Checks - Application Type Specific
        {{#eq APP_TYPE 'react-frontend'}}
        livenessProbe:
          httpGet:
            path: /health/
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"

